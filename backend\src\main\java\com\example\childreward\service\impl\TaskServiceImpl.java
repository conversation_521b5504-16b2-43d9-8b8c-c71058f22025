package com.example.childreward.service.impl;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.entity.TaskType;
import com.example.childreward.repository.PointRecordRepository;
import com.example.childreward.repository.TaskRepository;
import com.example.childreward.service.PointService;
import com.example.childreward.service.TaskService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final PointRecordRepository pointRecordRepository;
    private final PointService pointService;

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task createTask(Task task) {
        // 设置默认状态
        if (task.getStatus() == null) {
            task.setStatus(TaskStatus.NOT_STARTED);
        }
        // 确保scheduledDate字段被设置
        if (task.getScheduledDate() == null) {
            task.setScheduledDate(task.getDueDate() != null ? task.getDueDate() : LocalDate.now());
        }
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task updateTask(Long id, Task task) {
        Task existingTask = getTaskById(id);
        
        // 更新基本信息
        existingTask.setTitle(task.getTitle());
        existingTask.setDescription(task.getDescription());
        existingTask.setExpectedMinutes(task.getExpectedMinutes());
        existingTask.setBasePoints(task.getBasePoints());
        existingTask.setDueTime(task.getDueTime());
        existingTask.setDueDate(task.getDueDate());
        existingTask.setTaskType(task.getTaskType());
        
        // 更新计划日期
        if (task.getScheduledDate() != null) {
            existingTask.setScheduledDate(task.getScheduledDate());
        } else if (task.getDueDate() != null) {
            // 如果没有提供计划日期，但提供了截止日期，则使用截止日期作为计划日期
            existingTask.setScheduledDate(task.getDueDate());
        }
        
        return taskRepository.save(existingTask);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public void deleteTask(Long id) {
        Task task = getTaskById(id);
        taskRepository.delete(task);
    }

    @Override
    public Task getTaskById(Long id) {
        return taskRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("任务不存在: " + id));
    }

    @Override
    @Cacheable(value = "tasksByDate", key = "#date.toString()")
    public List<Task> getTasksByDate(LocalDate date) {
        return taskRepository.findTasksByDateOrderByStatusPriority(date);
    }

    @Override
    public List<Task> getAllTasks() {
        return taskRepository.findAll();
    }

    @Override
    public List<Task> getAllTasks(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return taskRepository.findAll(pageable).getContent();
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task startTask(Long id) {
        Task task = getTaskById(id);
        if (task.getStatus() != TaskStatus.NOT_STARTED) {
            throw new IllegalStateException("Task is not in a startable state.");
        }
        task.setStatus(TaskStatus.IN_PROGRESS);
        task.setStartTime(LocalDateTime.now());
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task completeTask(Long id) {
        Task task = getTaskById(id);
        if (task.getStatus() != TaskStatus.IN_PROGRESS) {
            throw new IllegalStateException("Task cannot be completed.");
        }
        
        // 奖励类型任务完成后进入审批流程，而不是直接标记为完成
        task.setStatus(TaskStatus.PENDING); // 修改为PENDING，等待家长审批
        task.setEndTime(LocalDateTime.now());
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task approveTask(Long id, Integer actualPoints) {
        Task task = getTaskById(id);
        if (task.getStatus() != TaskStatus.PENDING) {
            throw new IllegalStateException("只有处于待审批状态的任务才能被批准。");
        }
        task.setStatus(TaskStatus.COMPLETED);
        task.setActualPoints(actualPoints);

        // 添加积分逻辑 - 所有类型的任务完成后都应该获得积分
        if (actualPoints > 0) {
            pointService.addPoints(
                actualPoints,
                PointRecord.ChangeType.TASK_COMPLETION,
                "完成任务：" + task.getTitle(),
                task.getId()
            );
        }

        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task rejectTask(Long id, String reason) {
        Task task = getTaskById(id);
        // 允许从任何状态作废
        task.setStatus(TaskStatus.REJECTED);
        // 可以在这里添加作废原因的逻辑
        return taskRepository.save(task);
    }

    @Override
    @Cacheable(value = "pendingTasks", key = "'all'")
    public List<Task> getPendingTasks() {
        return taskRepository.findByStatus(TaskStatus.PENDING);
    }

    @Override
    public List<Task> getPendingTasksByDateRange(LocalDate startDate, LocalDate endDate) {
        // 如果没有指定日期范围，默认查询当天
        if (startDate == null && endDate == null) {
            LocalDate today = LocalDate.now();
            return taskRepository.findByStatusAndScheduledDateBetween(TaskStatus.PENDING, today, today);
        }

        // 如果只指定了开始日期，结束日期设为开始日期
        if (endDate == null) {
            endDate = startDate;
        }

        // 如果只指定了结束日期，开始日期设为结束日期
        if (startDate == null) {
            startDate = endDate;
        }

        return taskRepository.findByStatusAndScheduledDateBetween(TaskStatus.PENDING, startDate, endDate);
    }

    @Override
    public List<Task> getTasksByDateRangeAndStatus(LocalDate startDate, LocalDate endDate, String status) {
        // 如果没有指定日期范围，默认查询当天
        if (startDate == null && endDate == null) {
            LocalDate today = LocalDate.now();
            startDate = today;
            endDate = today;
        } else if (endDate == null) {
            endDate = startDate;
        } else if (startDate == null) {
            startDate = endDate;
        }

        // 如果没有指定状态，返回所有状态的任务
        if (status == null || status.trim().isEmpty()) {
            return taskRepository.findByScheduledDateBetween(startDate, endDate);
        }

        // 根据状态查询
        try {
            TaskStatus taskStatus = TaskStatus.valueOf(status.toUpperCase());
            return taskRepository.findByStatusAndScheduledDateBetween(taskStatus, startDate, endDate);
        } catch (IllegalArgumentException e) {
            // 如果状态无效，返回空列表
            return new ArrayList<>();
        }
    }

    @Override
    public List<Task> findOverdueTasks() {
        return taskRepository.findByStatusAndDueDateBefore(TaskStatus.NOT_STARTED, LocalDate.now());
    }

    @Override
    public List<Task> getPastTasks() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate sevenDaysAgo = yesterday.minusDays(6); // 最近7天，不包括今天

        return taskRepository.findPastTasksInDateRange(today, sevenDaysAgo);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public void processOverdueTasks() {
        // 获取昨天的未完成任务
        List<Task> overdueTasks = taskRepository.findOverdueTasks(LocalDate.now());
        
        for (Task task : overdueTasks) {
            // 标记为逾期
            task.setStatus(TaskStatus.OVERDUE);
            taskRepository.save(task);
            
            // 注意：TaskType的逻辑已改变，旧的判断逻辑已不再适用，暂时注释。
            // if (task.getTaskType() == Task.TaskType.REQUIRED) {
            //     // 对于必做任务，逾期可能会有惩罚，此处可以添加逻辑
            // }
        }
    }

    @Override
    public List<Task> getTasksByScheduledDateLessThanEqual(LocalDate date) {
        return taskRepository.findTasksByScheduledDateLessThanEqualOrderByStatusPriority(date);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task approvePenalty(Long taskId) {
        Task task = getTaskById(taskId);
        if (task.getStatus() != TaskStatus.PENDING) {
            throw new IllegalStateException("只有处于待审批状态的惩罚才能被批准。");
        }
        // 扣分，所以记录为负值
        task.setActualPoints(-task.getBasePoints());
        task.setStatus(TaskStatus.COMPLETED);
        task.setEndTime(LocalDateTime.now());

        // 执行扣分操作
        if (task.getTaskType() == TaskType.PENALTY_APPROVAL && task.getBasePoints() > 0) {
            pointService.deductPoints(
                task.getBasePoints(),
                PointRecord.ChangeType.TASK_PENALTY,
                "惩罚任务：" + task.getTitle(),
                task.getId()
            );
        }

        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task rejectPenalty(Long taskId) {
        Task task = getTaskById(taskId);
        if (task.getStatus() != TaskStatus.PENDING) {
            throw new IllegalStateException("只有处于待审批状态的惩罚才能被拒绝。");
        }
        task.setStatus(TaskStatus.REJECTED);
        return taskRepository.save(task);
    }
} 