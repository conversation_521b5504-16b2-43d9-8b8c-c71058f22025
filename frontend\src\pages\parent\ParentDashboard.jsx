import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts';
import { pointApi, taskApi, rewardApi } from '../../api/apiService';
import { format, subDays, parseISO } from 'date-fns';
import PunishmentPointCard from '../../components/parent/PunishmentPointCard';
import BonusPointCard from '../../components/parent/BonusPointCard';

const processWeeklyPointsData = (records) => {
  const weeklyData = {};
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  // 初始化最近7天的数据
  for (let i = 6; i >= 0; i--) {
    const date = subDays(new Date(), i);
    const dayKey = format(date, 'yyyy-MM-dd');
    weeklyData[dayKey] = {
      date: weekDays[date.getDay()],
      获得: 0,
      消费: 0,
    };
  }

  // 填充数据
  records.forEach(record => {
    const recordDate = new Date(record.recordTime);
    const dayKey = format(recordDate, 'yyyy-MM-dd');
    
    if (weeklyData[dayKey]) {
      if (record.pointChange > 0) {
        weeklyData[dayKey].获得 += record.pointChange;
      } else {
        weeklyData[dayKey].消费 += Math.abs(record.pointChange);
      }
    }
  });

  return Object.values(weeklyData);
};

// 模拟数据
const mockTaskCompletionData = [
  { name: '已完成', value: 12, color: '#4cd964' },
  { name: '进行中', value: 5, color: '#ffcc00' },
  { name: '未开始', value: 3, color: '#ff9500' },
  { name: '未开始任务', value: 2, color: '#ff3b30' }
];

const mockPointsData = [
  { date: '周一', 获得: 35, 消费: 25 },
  { date: '周二', 获得: 42, 消费: 10 },
  { date: '周三', 获得: 28, 消费: 40 },
  { date: '周四', 获得: 45, 消费: 15 },
  { date: '周五', 获得: 30, 消费: 30 },
  { date: '周六', 获得: 50, 消费: 20 },
  { date: '周日', 获得: 40, 消费: 60 },
];

const mockRecentActivities = [
  {
    id: 1,
    type: 'task',
    action: '完成任务',
    name: '完成数学作业',
    points: 10,
    time: '今天 09:45',
    icon: '📚'
  },
  {
    id: 2,
    type: 'reward',
    action: '抽取奖品',
    name: '小奖品池',
    points: -10,
    time: '今天 10:30',
    icon: '🎁'
  },
  {
    id: 3,
    type: 'task',
    action: '完成任务',
    name: '整理书桌',
    points: 5,
    time: '今天 12:15',
    icon: '🧹'
  },
  {
    id: 4,
    type: 'system',
    action: '过期未完成',
    name: '阅读故事书',
    points: -5,
    time: '昨天 22:00',
    icon: '⚠️'
  },
  {
    id: 5,
    type: 'reward',
    action: '抽取奖品',
    name: '中奖品池',
    points: -30,
    time: '昨天 18:45',
    icon: '🎁'
  }
];

const mockRewards = [
  { id: 1, name: '小奖池', count: 15 },
  { id: 2, name: '中奖池', count: 8 },
  { id: 3, name: '大奖池', count: 2 }
];

const ParentDashboard = () => {
  const [taskData, setTaskData] = useState([]);
  const [pointsData, setPointsData] = useState([]);
  const [activities, setActivities] = useState([]);
  const [rewards, setRewards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPoints, setTotalPoints] = useState(0);
  
  // 单独获取总积分的函数（用于扣分后刷新）
  const fetchTotalPoints = async () => {
    try {
      const response = await pointApi.getTotalPoints();
      setTotalPoints(response.data.totalPoints);
    } catch (error) {
      console.error("获取积分失败:", error);
    }
  };

  useEffect(() => {
    // 从API获取Dashboard数据
    const fetchDashboardData = async () => {
      try {
        // 获取今日任务
        const tasksResponse = await taskApi.getTasks();
        const tasks = tasksResponse.data;
        
        // 计算任务状态统计
        const completedTasks = tasks.filter(task => task.status === 'COMPLETED').length;
        const inProgressTasks = tasks.filter(task => task.status === 'IN_PROGRESS').length;
        const notStartedTasks = tasks.filter(task => task.status === 'NOT_STARTED').length;
        const overdueTasks = tasks.filter(task => task.status === 'OVERDUE').length;
        
        const taskStats = [
          { name: '已完成', value: completedTasks, color: '#4cd964' },
          { name: '进行中', value: inProgressTasks, color: '#ffcc00' },
          { name: '未开始', value: notStartedTasks, color: '#ff9500' },
          { name: '未开始任务', value: notStartedTasks, color: '#ff3b30' }
        ];
        
        setTaskData(taskStats);
        
        // 获取总积分
        const pointsResponse = await pointApi.getTotalPoints();
        setTotalPoints(pointsResponse.data.totalPoints);
        
        // 获取并处理最近7天的积分数据
        const recentRecordsResponse = await pointApi.getRecentPointRecords(7);
        const processedData = processWeeklyPointsData(recentRecordsResponse.data);
        setPointsData(processedData);
        
        // 获取积分记录，作为最近活动
        const recordsResponse = await pointApi.getPointRecords();
        const records = recordsResponse.data.slice(0, 5); // 只取前5条
        
        const formattedActivities = records.map(record => {
          let type = 'system';
          let action = '';
          let name = '';
          let icon = '⚙️';
          
          if (record.changeType === 'TASK_COMPLETION') {
            type = 'task';
            action = '完成任务';
            name = `任务 #${record.relatedTaskId}`;
            icon = '📚';
          } else if (record.changeType === 'TASK_PENALTY') {
            type = 'system';
            action = '过期未完成';
            name = `任务 #${record.relatedTaskId}`;
            icon = '⚠️';
          } else if (record.changeType === 'REWARD_EXCHANGE') {
            type = 'reward';
            action = '兑换奖品';
            name = `奖品 #${record.relatedRewardId}`;
            icon = '🎁';
          } else if (record.changeType === 'MANUAL_ADJUST') {
            type = 'system';
            action = '手动调整';
            name = record.description || '积分调整';
            icon = '⚙️';
          }
          
          return {
            id: record.id,
            type,
            action,
            name,
            points: record.pointChange,
            time: format(new Date(record.recordTime), 'yyyy-MM-dd HH:mm'),
            icon
          };
        });
        
        setActivities(formattedActivities);
        
        // 获取奖品池
        const poolsResponse = await rewardApi.getRewardPools();
        const formattedRewards = poolsResponse.data.map(pool => ({
          id: pool.id,
          name: pool.name,
          count: pool.costPoints
        }));
        
        setRewards(formattedRewards);
      } catch (error) {
        console.error('获取Dashboard数据失败:', error);
        // 如果API请求失败，使用默认值
        setTaskData([
          { name: '已完成', value: 0, color: '#4cd964' },
          { name: '进行中', value: 0, color: '#ffcc00' },
          { name: '未开始', value: 0, color: '#ff9500' },
          { name: '未开始任务', value: 0, color: '#ff3b30' }
        ]);
        setPointsData([]);
        setActivities([]);
        setRewards([]);
        setTotalPoints(0);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return <LoadingMessage>加载中...</LoadingMessage>;
  }

  const calculateTaskTotal = () => {
    return taskData.reduce((sum, item) => sum + item.value, 0);
  };

  return (
    <DashboardContainer>
      <Header>
        <Title>家长控制面板</Title>
        <DateDisplay>{new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' })}</DateDisplay>
      </Header>

      <ContentGrid>
        <MainContent>
          <StatCardsContainer>
            <StatCard>
              <StatIconWrapper color="#4cd964">
                <StatIcon>✅</StatIcon>
              </StatIconWrapper>
              <StatContent>
                <StatValue>{taskData.find(t => t.name === '已完成')?.value || 0}</StatValue>
                <StatLabel>已完成任务</StatLabel>
              </StatContent>
            </StatCard>

            <StatCard>
              <StatIconWrapper color="#ffcc00">
                <StatIcon>⏳</StatIcon>
              </StatIconWrapper>
              <StatContent>
                <StatValue>{taskData.find(t => t.name === '进行中')?.value || 0}</StatValue>
                <StatLabel>进行中任务</StatLabel>
              </StatContent>
            </StatCard>

            <StatCard>
              <StatIconWrapper color="#ff3b30">
                <StatIcon>📋</StatIcon>
              </StatIconWrapper>
              <StatContent>
                <StatValue>{taskData.find(t => t.name === '未开始任务')?.value || 0}</StatValue>
                <StatLabel>未开始任务</StatLabel>
              </StatContent>
            </StatCard>

            <StatCard>
              <StatIconWrapper color="#007aff">
                <StatIcon>🎮</StatIcon>
              </StatIconWrapper>
              <StatContent>
                <StatValue>{totalPoints}</StatValue>
                <StatLabel>当前积分</StatLabel>
              </StatContent>
            </StatCard>
          </StatCardsContainer>

          <ChartsSection>
            <ChartCard>
              <CardHeader>
                <CardTitle>任务完成率</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={220}>
                  <PieChart>
                    <Pie
                      data={taskData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent, value }) => {
                        // 只有当值大于0且百分比大于5%时才显示标签，避免重叠和0%显示
                        if (value > 0 && percent > 0.05) {
                          return `${name} ${(percent * 100).toFixed(0)}%`;
                        }
                        return '';
                      }}
                    >
                      {taskData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <ChartLegend>
                  {taskData.map(item => (
                    <LegendItem key={item.name}>
                      <LegendColor color={item.color} />
                      <LegendText>{item.name}: {item.value}</LegendText>
                    </LegendItem>
                  ))}
                  <TotalItem>总计: {calculateTaskTotal()}</TotalItem>
                </ChartLegend>
              </CardContent>
            </ChartCard>

            <ChartCard>
              <CardHeader>
                <CardTitle>一周积分变化</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={220}>
                  <BarChart
                    data={pointsData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 5 }}
                  >
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="获得" fill="#4cd964" />
                    <Bar dataKey="消费" fill="#ff9500" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </ChartCard>
          </ChartsSection>
        </MainContent>

        <SideSection>
          <BonusAndPunishmentContainer>
            <PunishmentPointCard onPointsUpdated={fetchTotalPoints} />
            <BonusPointCard onPointsUpdated={fetchTotalPoints} />
          </BonusAndPunishmentContainer>

          <SideCard>
            <CardHeader>
              <CardTitle>最近活动</CardTitle>
            </CardHeader>
            <ActivityList>
              {activities.map(activity => (
                <ActivityItem key={activity.id} type={activity.type}>
                  <ActivityIcon>{activity.icon}</ActivityIcon>
                  <ActivityContent>
                    <ActivityHeader>
                      <ActivityAction>{activity.action}</ActivityAction>
                      <ActivityTime>{activity.time}</ActivityTime>
                    </ActivityHeader>
                    <ActivityName>{activity.name}</ActivityName>
                  </ActivityContent>
                  <ActivityPoints isPositive={activity.points > 0}>
                    {activity.points > 0 ? '+' : ''}{activity.points}
                  </ActivityPoints>
                </ActivityItem>
              ))}
            </ActivityList>
          </SideCard>

          <SideCard>
            <CardHeader>
              <CardTitle>奖池概览</CardTitle>
            </CardHeader>
            <RewardList>
              {rewards.map(reward => (
                <RewardItem key={reward.id}>
                  <RewardIcon>🏆</RewardIcon>
                  <RewardName>{reward.name}</RewardName>
                  <RewardCount>{reward.count} 奖品</RewardCount>
                </RewardItem>
              ))}
            </RewardList>
          </SideCard>
        </SideSection>
      </ContentGrid>
    </DashboardContainer>
  );
};

// 样式组件
const DashboardContainer = styled.div`
  padding: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: ${parentTheme.textColor};
  margin: 0;
`;

const DateDisplay = styled.div`
  font-size: 1rem;
  color: #666;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 300px;
  grid-template-rows: auto 1fr;
  gap: 1.5rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const MainContent = styled.div`
  grid-column: 1;
  grid-row: 2;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const StatCardsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  grid-column: 1 / -1;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const StatCard = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
`;

const StatIconWrapper = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${props => `${props.color}10`};
  color: ${props => props.color};
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 1rem;
`;

const StatIcon = styled.span`
  font-size: 1.5rem;
`;

const StatContent = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${parentTheme.textColor};
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const ChartsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const ChartCard = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const CardHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #eee;
`;

const CardTitle = styled.h2`
  margin: 0;
  font-size: 1.1rem;
  color: ${parentTheme.textColor};
`;

const CardContent = styled.div`
  padding: 1rem;
`;

const ChartLegend = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
`;

const LegendColor = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${props => props.color};
  margin-right: 0.5rem;
`;

const LegendText = styled.span`
  font-size: 0.8rem;
  color: #666;
`;

const TotalItem = styled.div`
  font-size: 0.8rem;
  font-weight: 600;
  color: ${parentTheme.textColor};
  margin-left: auto;
`;

const SideSection = styled.div`
  grid-column: 2;
  grid-row: 2;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  
  @media (max-width: 1024px) {
    grid-column: 1;
    grid-row: 3;
  }
`;

const SideCard = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const ActivityList = styled.div`
  display: flex;
  flex-direction: column;
`;

const ActivityItem = styled.div`
  display: flex;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  background-color: ${props => 
    props.type === 'task' ? 'white' : 
    props.type === 'reward' ? 'rgba(255, 204, 0, 0.05)' : 
    'rgba(255, 59, 48, 0.05)'
  };
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityIcon = styled.div`
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 30px;
  text-align: center;
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
`;

const ActivityAction = styled.div`
  font-weight: 600;
  font-size: 0.9rem;
  color: ${parentTheme.textColor};
`;

const ActivityTime = styled.div`
  font-size: 0.8rem;
  color: #999;
`;

const ActivityName = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const ActivityPoints = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isPositive',
})`
  font-weight: 600;
  color: ${props => props.isPositive ? '#4cd964' : '#ff3b30'};
  font-size: 0.9rem;
  margin-left: 1rem;
  display: flex;
  align-items: center;
`;

const RewardList = styled.div`
  display: flex;
  flex-direction: column;
`;

const RewardItem = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
`;

const RewardIcon = styled.div`
  font-size: 1.25rem;
  margin-right: 0.75rem;
`;

const RewardName = styled.div`
  font-weight: 500;
  flex: 1;
  color: ${parentTheme.textColor};
`;

const RewardCount = styled.div`
  font-size: 0.85rem;
  color: #666;
`;

const LoadingMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: ${parentTheme.textColor};
`;

const BonusAndPunishmentContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
`;

export default ParentDashboard; 