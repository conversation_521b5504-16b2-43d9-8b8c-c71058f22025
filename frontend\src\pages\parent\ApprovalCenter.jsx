import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { taskApi } from '../../api/apiService';
import { format, parseISO } from 'date-fns';

// 模拟待审批任务数据
const mockApprovals = [
  {
    id: 1,
    childName: '小明',
    taskName: '完成数学作业',
    startTime: '2023-06-10 15:30',
    endTime: '2023-06-10 16:15',
    expectedDuration: 30, // 分钟
    actualDuration: 45, // 分钟
    basePoints: 10,
    status: 'pending', // pending, approved, rejected
    overdue: false,
    delayed: true, // 是否超时
    delayRate: 1.5, // 超时率
    effectivePoints: 8, // 实际可获得积分
  },
  {
    id: 2,
    childName: '小明',
    taskName: '整理书桌',
    startTime: '2023-06-10 17:00',
    endTime: '2023-06-10 17:12',
    expectedDuration: 15,
    actualDuration: 12,
    basePoints: 5,
    status: 'pending',
    overdue: false,
    delayed: false,
    delayRate: 0.8,
    effectivePoints: 5,
  },
  {
    id: 3,
    childName: '小明',
    taskName: '阅读故事书',
    startTime: '2023-06-09 19:00',
    endTime: '2023-06-09 19:18',
    expectedDuration: 20,
    actualDuration: 18,
    basePoints: 8,
    status: 'pending',
    overdue: false,
    delayed: false,
    delayRate: 0.9,
    effectivePoints: 8,
  },
  {
    id: 4,
    childName: '小明',
    taskName: '写日记',
    startTime: '2023-06-08 20:00',
    endTime: '2023-06-08 20:35',
    expectedDuration: 15,
    actualDuration: 35,
    basePoints: 6,
    status: 'approved',
    overdue: false,
    delayed: true,
    delayRate: 2.33,
    effectivePoints: 3,
  },
  {
    id: 5,
    childName: '小明',
    taskName: '英语单词复习',
    startTime: '2023-06-07 16:00',
    endTime: '2023-06-07 16:25',
    expectedDuration: 30,
    actualDuration: 25,
    basePoints: 10,
    status: 'approved',
    overdue: false,
    delayed: false,
    delayRate: 0.83,
    effectivePoints: 10,
  },
  {
    id: 6,
    childName: '小明',
    taskName: '练钢琴',
    startTime: '2023-06-06 18:00',
    endTime: '2023-06-06 18:28',
    expectedDuration: 30,
    actualDuration: 28,
    basePoints: 12,
    status: 'rejected',
    overdue: false,
    delayed: false,
    delayRate: 0.93,
    effectivePoints: 0,
  }
];

const ApprovalCenter = () => {
  const [approvals, setApprovals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('PENDING');
  const [selectedApproval, setSelectedApproval] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [batchMode, setBatchMode] = useState(false);
  const [selectedApprovals, setSelectedApprovals] = useState(new Set());

  // 日期范围状态
  const [startDate, setStartDate] = useState(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // 默认今天
  });
  const [endDate, setEndDate] = useState(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // 默认今天
  });

  useEffect(() => {
    // 从API获取任务
    const fetchApprovals = async () => {
      setLoading(true);
      try {
        let taskList = [];
        
        // 获取所有任务用于调试
        const allTasksResponse = await taskApi.getTasks();
        console.log('所有任务状态：', allTasksResponse.data.map(t => ({id: t.id, title: t.title, status: t.status})));
        
        // 使用新的日期范围查询API
        if (activeTab === 'all') {
          // 获取所有状态的任务
          const allResponse = await taskApi.getTasksByDateRangeAndStatus(startDate, endDate, null);
          console.log('所有状态任务：', allResponse.data.map(t => ({id: t.id, title: t.title, status: t.status})));
          const formattedTasks = formatTasks(allResponse.data, null);
          taskList = [...taskList, ...formattedTasks];
        } else {
          // 获取特定状态的任务
          const response = await taskApi.getTasksByDateRangeAndStatus(startDate, endDate, activeTab);
          console.log(`${activeTab}状态任务：`, response.data.map(t => ({id: t.id, title: t.title, status: t.status})));
          const formattedTasks = formatTasks(response.data, activeTab);
          taskList = [...taskList, ...formattedTasks];
        }
        
        console.log('过滤后的任务列表：', taskList);
        setApprovals(taskList);
      } catch (error) {
        console.error('获取任务失败:', error);
        setApprovals([]);
      } finally {
        setLoading(false);
      }
    };

    fetchApprovals();
  }, [activeTab, startDate, endDate]); // 当标签切换或日期范围变化时重新获取数据
  
  // 格式化任务数据的函数
  const formatTasks = (tasks, status) => {
    return tasks.map(task => {
      // 计算耗时
      const startTime = task.startTime ? new Date(task.startTime) : null;
      const endTime = task.endTime ? new Date(task.endTime) : null;

      let actualDuration;
      if (startTime && endTime) {
        const totalSeconds = (endTime.getTime() - startTime.getTime()) / 1000;
        // 不满1分钟按1分钟计算（向上取整）
        actualDuration = totalSeconds > 0 ? Math.ceil(totalSeconds / 60) : 0;
      } else {
        actualDuration = task.expectedMinutes;
      }

      // 计算延迟率
      const delayRate = task.expectedMinutes > 0 ? 
        (actualDuration / task.expectedMinutes).toFixed(2) : 1;

      return {
        id: task.id,
        childName: '小明', // 暂时硬编码，未来可能从用户系统获取
        taskName: task.title,
        startTime: startTime ? format(startTime, 'yyyy-MM-dd HH:mm') : null,
        endTime: endTime ? format(endTime, 'yyyy-MM-dd HH:mm') : null,
        expectedDuration: task.expectedMinutes,
        actualDuration: actualDuration,
        basePoints: task.basePoints,
        status: task.status, // 使用任务实际的状态
        overdue: task.status === 'OVERDUE',
        delayed: actualDuration > task.expectedMinutes,
        delayRate: parseFloat(delayRate),
        effectivePoints: task.status === 'COMPLETED' ? (task.actualPoints || task.basePoints) : (task.status === 'REJECTED' ? 0 : task.basePoints)
      };
    });
  };

  const handleViewDetails = (approval) => {
    setSelectedApproval(approval);
    setShowModal(true);
  };

  const handleApproveTask = async (id, reduced = false) => {
    try {
      const approval = approvals.find(a => a.id === id);
      if (!approval) return;
      
      // 计算实际积分
      const actualPoints = reduced 
        ? Math.floor(approval.basePoints * 0.8) 
        : approval.basePoints;
        
      // 调用API完成审批
      await taskApi.approveTask(id, actualPoints);
      
      // 更新本地状态
      setApprovals(prevApprovals => 
        prevApprovals.map(approval => 
          approval.id === id
            ? { 
                ...approval, 
                status: 'COMPLETED',
                effectivePoints: actualPoints
              }
            : approval
        )
      );
      
      if (selectedApproval && selectedApproval.id === id) {
        setShowModal(false);
      }
    } catch (error) {
      console.error('审批任务失败:', error);
      alert('审批任务失败，请稍后再试');
    }
  };

  const handleRejectTask = async (id) => {
    try {
      // 拒绝任务
      await taskApi.rejectTask(id);
      
      // 更新本地状态
      setApprovals(prevApprovals => 
        prevApprovals.map(approval => 
          approval.id === id
            ? { ...approval, status: 'REJECTED', effectivePoints: 0 }
            : approval
        )
      );
      
      if (selectedApproval && selectedApproval.id === id) {
        setShowModal(false);
      }
    } catch (error) {
      console.error('拒绝任务失败:', error);
      alert('拒绝任务失败，请稍后再试');
    }
  };

  const handleBatchApprove = async () => {
    if (selectedApprovals.size === 0) return;
    
    try {
      // 逐一审批选中的任务
      const promises = Array.from(selectedApprovals).map(id => {
        const approval = approvals.find(a => a.id === id);
        return taskApi.approveTask(id, approval.basePoints);
      });
      
      await Promise.all(promises);
      
      // 更新本地状态
      setApprovals(prevApprovals => 
        prevApprovals.map(approval => 
          selectedApprovals.has(approval.id)
            ? { ...approval, status: 'COMPLETED', effectivePoints: approval.basePoints }
            : approval
        )
      );
      
      setSelectedApprovals(new Set());
      setBatchMode(false);
    } catch (error) {
      console.error('批量审批任务失败:', error);
      alert('批量审批任务失败，请稍后再试');
    }
  };

  const handleBatchReject = async () => {
    if (selectedApprovals.size === 0) return;
    
    try {
      // 逐一拒绝选中的任务
      const promises = Array.from(selectedApprovals).map(id => taskApi.rejectTask(id));
      await Promise.all(promises);
      
      // 更新本地状态
      setApprovals(prevApprovals => 
        prevApprovals.map(approval => 
          selectedApprovals.has(approval.id)
            ? { ...approval, status: 'REJECTED', effectivePoints: 0 }
            : approval
        )
      );
      
      setSelectedApprovals(new Set());
      setBatchMode(false);
    } catch (error) {
      console.error('批量拒绝任务失败:', error);
      alert('批量拒绝任务失败，请稍后再试');
    }
  };

  const handleToggleSelect = (id) => {
    const newSelected = new Set(selectedApprovals);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedApprovals(newSelected);
  };

  const handleCancelBatch = () => {
    setBatchMode(false);
    setSelectedApprovals(new Set());
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}小时${mins > 0 ? ` ${mins}分钟` : ''}`;
    }
  };

  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) {
      return '-';
    }

    const date = new Date(dateTimeStr);
    if (isNaN(date.getTime())) {
      return '-';
    }

    return date.toLocaleString('zh-CN', {
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFilteredApprovals = () => {
    if (activeTab === 'all') return approvals;
    
    return approvals.filter(approval => {
      return approval.status === activeTab.toUpperCase();
    });
  };

  if (loading) {
    return <LoadingMessage>加载中...</LoadingMessage>;
  }

  const pendingCount = approvals.filter(a => a.status === 'PENDING').length;
  const approvedCount = approvals.filter(a => a.status === 'COMPLETED').length;
  const rejectedCount = approvals.filter(a => a.status === 'REJECTED').length;

  return (
    <ApprovalContainer>
      <Header>
        <HeaderLeft>
          <Title>审批中心</Title>
        </HeaderLeft>

        <HeaderRight>
          <DateRangeContainer>
            <DateLabel>查询日期：</DateLabel>
            <DateInput
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
            <DateSeparator>至</DateSeparator>
            <DateInput
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </DateRangeContainer>

          {pendingCount > 0 && !batchMode && (
            <BatchButton
              onClick={() => setBatchMode(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              批量审批
            </BatchButton>
          )}
        </HeaderRight>
        
        {batchMode && (
          <BatchActions>
            <SelectedCount>已选择 {selectedApprovals.size} 项</SelectedCount>
            <ApproveButton 
              onClick={handleBatchApprove}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={selectedApprovals.size === 0}
            >
              批量通过
            </ApproveButton>
            <RejectButton 
              onClick={handleBatchReject}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={selectedApprovals.size === 0}
            >
              批量拒绝
            </RejectButton>
            <CancelButton 
              onClick={handleCancelBatch}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              取消
            </CancelButton>
          </BatchActions>
        )}
      </Header>

      <TabsContainer>
        <Tab 
          $isActive={activeTab === 'PENDING'}
          onClick={() => setActiveTab('PENDING')}
        >
          待审批
          {pendingCount > 0 && <TabBadge>{pendingCount}</TabBadge>}
        </Tab>
        <Tab 
          $isActive={activeTab === 'COMPLETED'}
          onClick={() => setActiveTab('COMPLETED')}
        >
          已通过
        </Tab>
        <Tab 
          $isActive={activeTab === 'REJECTED'}
          onClick={() => setActiveTab('REJECTED')}
        >
          已拒绝
        </Tab>
        <Tab 
          $isActive={activeTab === 'all'}
          onClick={() => setActiveTab('all')}
        >
          全部
        </Tab>
      </TabsContainer>

      <ApprovalList>
        {getFilteredApprovals().length === 0 ? (
          <EmptyMessage>
            <EmptyIcon>📝</EmptyIcon>
            <EmptyText>
              {activeTab === 'PENDING' 
                ? '没有待审批的任务' 
                : activeTab === 'COMPLETED'
                ? '没有已通过的任务'
                : activeTab === 'REJECTED'
                ? '没有已拒绝的任务'
                : '没有任何任务记录'}
            </EmptyText>
          </EmptyMessage>
        ) : (
          <>
            <ListHeader>
              {batchMode && <HeaderCell width="5%"></HeaderCell>}
              <HeaderCell width="20%">任务名称</HeaderCell>
              <HeaderCell width="15%">开始时间</HeaderCell>
              <HeaderCell width="15%">完成时间</HeaderCell>
              <HeaderCell width="15%">耗时</HeaderCell>
              <HeaderCell width="10%">基础积分</HeaderCell>
              <HeaderCell width="10%">实际积分</HeaderCell>
              <HeaderCell width="15%">操作</HeaderCell>
            </ListHeader>
            
            <AnimatePresence>
              {getFilteredApprovals().map(approval => (
                <ApprovalItem
                  key={approval.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  isDelayed={approval.delayed}
                  isOverdue={approval.overdue}
                >
                  {batchMode && (
                    <ApprovalCell width="5%">
                      <Checkbox
                        type="checkbox"
                        checked={selectedApprovals.has(approval.id)}
                        onChange={() => handleToggleSelect(approval.id)}
                        disabled={approval.status !== 'PENDING'}
                      />
                    </ApprovalCell>
                  )}
                  <ApprovalCell width="20%">{approval.taskName}</ApprovalCell>
                  <ApprovalCell width="15%">{formatDateTime(approval.startTime)}</ApprovalCell>
                  <ApprovalCell width="15%">{formatDateTime(approval.endTime)}</ApprovalCell>
                  <ApprovalCell width="15%">
                    <DurationInfo isDelayed={approval.delayed}>
                      {formatDuration(approval.actualDuration)}
                      <small>
                        {approval.delayed
                          ? ` (超出预期${Math.round((approval.delayRate - 1) * 100)}%)`
                          : ` (预期${formatDuration(approval.expectedDuration)})`}
                      </small>
                    </DurationInfo>
                  </ApprovalCell>
                  <ApprovalCell width="10%">{approval.basePoints}</ApprovalCell>
                  <ApprovalCell width="10%">
                    <PointsValue isReduced={approval.effectivePoints < approval.basePoints}>
                      {approval.effectivePoints}
                    </PointsValue>
                  </ApprovalCell>
                  <ApprovalCell width="15%">
                    {approval.status === 'PENDING' ? (
                      <ActionButtons>
                        <ActionButton 
                          onClick={() => handleViewDetails(approval)}
                          color="primary"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          查看
                        </ActionButton>
                        <ActionButton 
                          onClick={() => handleApproveTask(approval.id)}
                          color="success"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          通过
                        </ActionButton>
                        <ActionButton 
                          onClick={() => handleRejectTask(approval.id)}
                          color="danger"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          拒绝
                        </ActionButton>
                      </ActionButtons>
                    ) : (
                      <StatusLabel status={approval.status}>
                        {approval.status === 'COMPLETED' ? '已通过' : '已拒绝'}
                      </StatusLabel>
                    )}
                  </ApprovalCell>
                </ApprovalItem>
              ))}
            </AnimatePresence>
          </>
        )}
      </ApprovalList>

      {/* 任务详情模态框 */}
      <AnimatePresence>
        {showModal && selectedApproval && (
          <ModalOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <ModalContainer
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
            >
              <ModalHeader>
                <ModalTitle>任务详情</ModalTitle>
                <CloseButton onClick={() => setShowModal(false)}>×</CloseButton>
              </ModalHeader>

              <ModalBody>
                <DetailItem>
                  <DetailLabel>任务名称</DetailLabel>
                  <DetailValue>{selectedApproval.taskName}</DetailValue>
                </DetailItem>
                
                <DetailItem>
                  <DetailLabel>执行时间</DetailLabel>
                  <DetailValue>
                    {formatDateTime(selectedApproval.startTime)} 至 {formatDateTime(selectedApproval.endTime)}
                  </DetailValue>
                </DetailItem>
                
                <DetailGrid>
                  <DetailItem>
                    <DetailLabel>预计耗时</DetailLabel>
                    <DetailValue>{formatDuration(selectedApproval.expectedDuration)}</DetailValue>
                  </DetailItem>
                  
                  <DetailItem>
                    <DetailLabel>实际耗时</DetailLabel>
                    <DetailValue 
                      isHighlighted={selectedApproval.delayed}
                      isNegative={selectedApproval.delayed}
                    >
                      {formatDuration(selectedApproval.actualDuration)}
                    </DetailValue>
                  </DetailItem>
                </DetailGrid>
                
                {selectedApproval.delayed && (
                  <WarningMessage>
                    <WarningIcon>⚠️</WarningIcon>
                    <WarningText>
                      任务耗时超过预期{Math.round((selectedApproval.delayRate - 1) * 100)}%，
                      建议按照实际系数{Math.min(1, (1 / selectedApproval.delayRate)).toFixed(2)}计算积分
                    </WarningText>
                  </WarningMessage>
                )}
                
                <DetailGrid>
                  <DetailItem>
                    <DetailLabel>基础积分</DetailLabel>
                    <DetailValue>{selectedApproval.basePoints}</DetailValue>
                  </DetailItem>
                  
                  <DetailItem>
                    <DetailLabel>建议积分</DetailLabel>
                    <DetailValue>{selectedApproval.effectivePoints}</DetailValue>
                  </DetailItem>
                </DetailGrid>
                
                <ModalActions>
                  <RejectButton
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleRejectTask(selectedApproval.id)}
                  >
                    拒绝任务
                  </RejectButton>
                  
                  {selectedApproval.delayed && (
                    <ReduceButton
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleApproveTask(selectedApproval.id, true)}
                    >
                      降低积分并通过
                    </ReduceButton>
                  )}
                  
                  <ApproveButton
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleApproveTask(selectedApproval.id)}
                  >
                    完全通过
                  </ApproveButton>
                </ModalActions>
              </ModalBody>
            </ModalContainer>
          </ModalOverlay>
        )}
      </AnimatePresence>
    </ApprovalContainer>
  );
};

// 样式组件
const ApprovalContainer = styled.div`
  padding: 1.5rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    margin-bottom: 1.5rem;
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: ${parentTheme.textColor};
  margin: 0;

  @media (max-width: 768px) {
    font-size: 1.5rem;
    text-align: center;
  }
`;

const DateRangeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
    padding: 0.75rem;
    width: 100%;
  }
`;

const DateLabel = styled.span`
  font-size: 0.9rem;
  color: #666;
  white-space: nowrap;
`;

const DateInput = styled.input`
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.4rem 0.6rem;
  font-size: 0.9rem;
  color: #333;
  background: white;

  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  @media (max-width: 768px) {
    width: 100%;
    padding: 0.6rem 0.8rem;
    font-size: 1rem;
  }
`;

const DateSeparator = styled.span`
  font-size: 0.9rem;
  color: #666;
  margin: 0 0.25rem;
`;

const BatchButton = styled(motion.button)`
  padding: 0.6rem 1.2rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-weight: 600;
  cursor: pointer;

  @media (max-width: 768px) {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
`;

const BatchActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;

  @media (max-width: 768px) {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
    margin-top: 1rem;
  }
`;

const SelectedCount = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    justify-content: center;
    margin-bottom: 1rem;
  }
`;

const Tab = styled.div`
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  color: ${props => props.$isActive ? parentTheme.primaryColor : '#666'};
  border-bottom: 2px solid ${props => props.$isActive ? parentTheme.primaryColor : 'transparent'};
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;

  &:hover {
    color: ${parentTheme.primaryColor};
  }

  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    flex: 1;
    text-align: center;
  }
`;

const TabBadge = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${parentTheme.secondaryColor};
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-size: 0.7rem;
  margin-left: 0.5rem;
`;

const ApprovalList = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  @media (max-width: 768px) {
    background: transparent;
    box-shadow: none;
    border-radius: 0;
  }
`;

const ListHeader = styled.div`
  display: flex;
  padding: 1rem;
  background-color: #f5f7fa;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #666;

  @media (max-width: 768px) {
    display: none; /* 手机端隐藏表头 */
  }
`;

const HeaderCell = styled.div`
  width: ${props => props.width};
  padding: 0 0.5rem;
`;

const ApprovalItem = styled(motion.div)`
  display: flex;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  background-color: ${props => {
    if (props.isOverdue) return 'rgba(255, 59, 48, 0.05)';
    if (props.isDelayed) return 'rgba(255, 204, 0, 0.05)';
    return 'white';
  }};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: ${props => {
      if (props.isOverdue) return 'rgba(255, 59, 48, 0.1)';
      if (props.isDelayed) return 'rgba(255, 204, 0, 0.1)';
      return 'rgba(0, 0, 0, 0.02)';
    }};
  }

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: ${parentTheme.borderRadius};
    border: 1px solid #eee;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const ApprovalCell = styled.div`
  width: ${props => props.width};
  padding: 0 0.5rem;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    width: 100% !important;
    padding: 0.25rem 0;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
      margin-top: 0.5rem;
      justify-content: center;
    }

    /* 为移动端添加标签 */
    &:before {
      content: attr(data-label);
      font-weight: 600;
      color: #666;
      font-size: 0.85rem;
      min-width: 80px;
    }

    &:first-child:before {
      content: "任务名称: ";
    }

    &:nth-child(2):before {
      content: "开始时间: ";
    }

    &:nth-child(3):before {
      content: "完成时间: ";
    }

    &:nth-child(4):before {
      content: "耗时: ";
    }

    &:nth-child(5):before {
      content: "基础积分: ";
    }

    &:nth-child(6):before {
      content: "实际积分: ";
    }

    &:nth-child(7):before {
      content: "";
    }
  }
`;

const DurationInfo = styled.div`
  display: flex;
  flex-direction: column;
  color: ${props => props.isDelayed ? parentTheme.secondaryColor : 'inherit'};
  
  small {
    color: #999;
    margin-top: 0.25rem;
  }
`;

const PointsValue = styled.div`
  font-weight: 600;
  color: ${props => props.isReduced ? parentTheme.secondaryColor : parentTheme.primaryColor};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled(motion.button)`
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  background-color: ${props => 
    props.color === 'danger' 
      ? 'rgba(255, 59, 48, 0.1)' 
      : props.color === 'success'
      ? 'rgba(76, 217, 100, 0.1)'
      : 'rgba(0, 122, 255, 0.1)'
  };
  color: ${props => 
    props.color === 'danger' 
      ? '#ff3b30' 
      : props.color === 'success'
      ? '#4cd964'
      : '#007aff'
  };
  
  &:hover {
    background-color: ${props => 
      props.color === 'danger' 
        ? 'rgba(255, 59, 48, 0.2)' 
        : props.color === 'success'
        ? 'rgba(76, 217, 100, 0.2)'
        : 'rgba(0, 122, 255, 0.2)'
    };
  }
`;

const StatusLabel = styled.div`
  display: inline-block;
  padding: 0.3rem 0.6rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${props =>
    props.status === 'COMPLETED'
      ? 'rgba(76, 217, 100, 0.1)'
      : 'rgba(255, 59, 48, 0.1)'
  };
  color: ${props =>
    props.status === 'COMPLETED'
      ? '#4cd964'
      : '#ff3b30'
  };
`;

const EmptyMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
`;

const EmptyText = styled.div`
  color: #999;
  font-size: 1.1rem;
`;

const LoadingMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: ${parentTheme.textColor};
`;

const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled(motion.div)`
  width: 90%;
  max-width: 600px;
  background-color: white;
  border-radius: ${parentTheme.borderRadius};
  overflow: hidden;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.25rem;
  color: ${parentTheme.textColor};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  
  &:hover {
    color: #666;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const DetailItem = styled.div`
  margin-bottom: 1.5rem;
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
`;

const DetailLabel = styled.div`
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DetailValue = styled.div`
  font-size: 1.1rem;
  font-weight: 500;
  color: ${props => 
    props.isHighlighted 
      ? props.isNegative 
        ? parentTheme.secondaryColor
        : parentTheme.primaryColor
      : parentTheme.textColor
  };
`;

const WarningMessage = styled.div`
  background-color: rgba(255, 204, 0, 0.1);
  border-left: 4px solid ${parentTheme.secondaryColor};
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
`;

const WarningIcon = styled.span`
  font-size: 1.5rem;
  margin-right: 1rem;
`;

const WarningText = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const ModalActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
`;

const BaseButton = styled(motion.button)`
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ApproveButton = styled(BaseButton)`
  background: ${parentTheme.gradients.primary};
  color: white;
`;

const ReduceButton = styled(BaseButton)`
  background-color: #FFB800;
  color: white;
`;

const RejectButton = styled(BaseButton)`
  background-color: #ff3b30;
  color: white;
`;

const CancelButton = styled(BaseButton)`
  background-color: #f5f5f5;
  color: #666;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

export default ApprovalCenter; 