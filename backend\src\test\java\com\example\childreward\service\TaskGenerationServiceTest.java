package com.example.childreward.service;

import com.example.childreward.entity.ScheduledTask;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.entity.TaskType;
import com.example.childreward.repository.ScheduledTaskRepository;
import com.example.childreward.repository.TaskRepository;
import com.example.childreward.util.TaskTypeConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.support.CronExpression;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskGenerationServiceTest {

    @Mock
    private ScheduledTaskRepository scheduledTaskRepository;

    @Mock
    private TaskRepository taskRepository;

    @InjectMocks
    private TaskGenerationService taskGenerationService;

    @Captor
    private ArgumentCaptor<Task> taskCaptor;

    private ScheduledTask rewardTemplate;
    private ScheduledTask penaltyTemplate;

    @BeforeEach
    void setUp() {
        // 使用构造函数和setter方法创建测试对象
        rewardTemplate = new ScheduledTask();
        rewardTemplate.setId(1L);
        rewardTemplate.setTitle("每日阅读");
        rewardTemplate.setCronExpression("0 0 1 * * ?"); // 每天凌晨1点
        rewardTemplate.setType(TaskType.REWARD);
        rewardTemplate.setPoints(10);
        rewardTemplate.setExpectedMinutes(30);
        rewardTemplate.setDueTime(LocalTime.of(21, 0));
        rewardTemplate.setActive(true);

        penaltyTemplate = new ScheduledTask();
        penaltyTemplate.setId(2L);
        penaltyTemplate.setTitle("每日惩罚");
        penaltyTemplate.setCronExpression("0 0 1 * * ?"); // 每天凌晨1点
        penaltyTemplate.setType(TaskType.PENALTY_APPROVAL);
        penaltyTemplate.setPoints(5);
        penaltyTemplate.setExpectedMinutes(15);
        penaltyTemplate.setDueTime(LocalTime.of(18, 0));
        penaltyTemplate.setActive(true);
    }

    @Test
    void generateTasksFromTemplates_shouldCreateTasksFromActiveTemplates() {
        // Given
        List<ScheduledTask> activeTemplates = Arrays.asList(rewardTemplate, penaltyTemplate);
        when(scheduledTaskRepository.findByActiveTrue()).thenReturn(activeTemplates);
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        taskGenerationService.generateTasksFromTemplates();

        // Then
        verify(taskRepository, times(2)).save(taskCaptor.capture());
        
        List<Task> capturedTasks = taskCaptor.getAllValues();
        
        // 验证第一个任务（奖励类型）
        Task rewardTask = capturedTasks.get(0);
        assertEquals(rewardTemplate.getId(), rewardTask.getSourceTemplateId());
        assertEquals(rewardTemplate.getTitle(), rewardTask.getTitle());
        assertEquals(rewardTemplate.getType(), rewardTask.getTaskType());
        assertEquals(rewardTemplate.getPoints(), rewardTask.getBasePoints());
        assertEquals(rewardTemplate.getExpectedMinutes(), rewardTask.getExpectedMinutes());
        assertEquals(LocalDate.now(), rewardTask.getDueDate());
        assertEquals(rewardTemplate.getDueTime(), rewardTask.getDueTime());
        assertEquals(TaskStatus.NOT_STARTED, rewardTask.getStatus());
        
        // 验证第二个任务（惩罚类型）
        Task penaltyTask = capturedTasks.get(1);
        assertEquals(penaltyTemplate.getId(), penaltyTask.getSourceTemplateId());
        assertEquals(penaltyTemplate.getTitle(), penaltyTask.getTitle());
        assertEquals(penaltyTemplate.getType(), penaltyTask.getTaskType());
        assertEquals(penaltyTemplate.getPoints(), penaltyTask.getBasePoints());
        assertEquals(penaltyTemplate.getExpectedMinutes(), penaltyTask.getExpectedMinutes());
        assertEquals(LocalDate.now(), penaltyTask.getDueDate());
        assertEquals(penaltyTemplate.getDueTime(), penaltyTask.getDueTime());
        assertEquals(TaskStatus.PENDING, penaltyTask.getStatus());
    }

    @Test
    void generateTasksFromTemplates_shouldHandleInvalidCronExpression() {
        // Given
        ScheduledTask invalidTemplate = new ScheduledTask();
        invalidTemplate.setId(3L);
        invalidTemplate.setTitle("无效模板");
        invalidTemplate.setCronExpression("invalid cron");
        invalidTemplate.setType(TaskType.REWARD);
        invalidTemplate.setPoints(5);
        invalidTemplate.setActive(true);

        List<ScheduledTask> activeTemplates = Arrays.asList(rewardTemplate, invalidTemplate);
        when(scheduledTaskRepository.findByActiveTrue()).thenReturn(activeTemplates);
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        taskGenerationService.generateTasksFromTemplates();

        // Then
        // 只有一个有效的模板应该创建任务
        verify(taskRepository, times(1)).save(any(Task.class));
    }

    @Test
    void generateTasksFromTemplates_shouldUseDefaultValuesWhenMissing() {
        // Given
        ScheduledTask minimalTemplate = new ScheduledTask();
        minimalTemplate.setId(4L);
        minimalTemplate.setTitle("最小模板");
        minimalTemplate.setCronExpression("0 0 1 * * ?");
        minimalTemplate.setType(TaskType.REWARD);
        minimalTemplate.setPoints(5);
        minimalTemplate.setActive(true);
        // 缺少expectedMinutes和dueTime

        List<ScheduledTask> activeTemplates = Arrays.asList(minimalTemplate);
        when(scheduledTaskRepository.findByActiveTrue()).thenReturn(activeTemplates);
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        taskGenerationService.generateTasksFromTemplates();

        // Then
        verify(taskRepository).save(taskCaptor.capture());
        
        Task createdTask = taskCaptor.getValue();
        assertEquals(60, createdTask.getExpectedMinutes()); // 默认值为60分钟
        assertEquals(LocalTime.of(23, 59, 59), createdTask.getDueTime()); // 默认为当天午夜
    }
} 