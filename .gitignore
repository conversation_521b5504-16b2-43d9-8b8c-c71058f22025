# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace
*.sublime-*
.atom/
.brackets.json
.history/
.ionide/

# 日志文件
logs/
*.log
*.log.*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 上传文件目录
upload/
uploads/

# 临时文件
tmp/
temp/
.tmp/

# 缓存文件
.cache/
*.cache

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# Java相关
backend/target/
backend/.mvn/
backend/mvnw
backend/mvnw.cmd
target/
.mvn/
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
.factorypath
.project
.settings/
.classpath
.springBeans
.sts4-cache/
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# Node.js相关
node_modules/
frontend/node_modules/
frontend/dist/
frontend/build/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm/
.yarn/
.pnp.*
.yarn-integrity

# 防止根目录意外安装Node.js包
/package.json
/package-lock.json
/yarn.lock
/pnpm-lock.yaml
/node_modules/

# 打包输出目录
packages/
temp-package/

# 系统特定文件
*.orig
*.rej
.nfs*

# 测试覆盖率报告
coverage/
*.lcov

# 构建输出
build/
dist/
out/
lib/
lib-cov/

# 包管理器锁文件（可选择性忽略）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ESLint缓存
.eslintcache

# Stylelint缓存
.stylelintcache

# Parcel缓存
.parcel-cache/

# Next.js构建输出
.next/

# Nuxt.js构建输出
.nuxt/

# Gatsby文件
.cache/
public/

# Storybook构建输出
storybook-static/

# Temporary folders
.tmp/
.temp/

# 本地配置文件
config/local.json
config/local.yml
config/local.yaml

# 证书文件
*.pem
*.key
*.crt
*.p12
*.pfx

# 文档生成文件
docs/build/
site/

# Python相关（微信作业监控项目）
__pycache__/
*.py[cod]
*.so
*.egg
*.egg-info/
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# 微信作业监控项目特定文件
wechat-homework-monitor/logs/
wechat-homework-monitor/data/
wechat-homework-monitor/config_backups/
wechat-homework-monitor/release/
wechat-homework-monitor/build/
wechat-homework-monitor/dist/
wechat-homework-monitor/*.exe
wechat-homework-monitor/*.spec
wechat-homework-monitor/screenshots/
wechat-homework-monitor/first_run_completed.txt
wechat-homework-monitor/微信作业监听系统_*.zip

# OCR和AI相关临时文件
wechat-homework-monitor/*_processed.png
wechat-homework-monitor/*_debug.png
wechat-homework-monitor/test_*.py
wechat-homework-monitor/debug_*.py
wechat-homework-monitor/manual_*.py
wechat-homework-monitor/precise_*.py
wechat-homework-monitor/realtime_*.py
wechat-homework-monitor/simple_*.py

# 本地配置文件
wechat-homework-monitor/config_local.yaml
wechat-homework-monitor/config_local.yml
