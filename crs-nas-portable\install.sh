#!/bin/bash
set -e
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"
echo "Installing JDK21..."
if [ ! -d "jdk" ]; then
  echo "Downloading JDK21..."
  wget -O jdk21.tar.gz "https://github.com/adoptium/temurin21-binaries/releases/download/jdk-21.0.1+12/OpenJDK21U-jdk_x64_linux_hotspot_21.0.1_12.tar.gz"
  tar -xzf jdk21.tar.gz
  mv jdk-21.0.1+12 jdk
  rm jdk21.tar.gz
  chmod +x jdk/bin/*
  echo "JDK21 installed successfully"
else
  echo "JDK21 already exists"
fi
echo "Installation completed!"