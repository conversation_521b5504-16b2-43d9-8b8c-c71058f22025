package com.example.childreward.repository;

import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {

    List<Task> findByDueDate(LocalDate dueDate);
    
    List<Task> findByDueDateAndStatus(LocalDate dueDate, TaskStatus status);
    
    @Query("SELECT t FROM Task t WHERE t.dueDate = ?1 AND t.status NOT IN ('COMPLETED', 'OVERDUE')")
    List<Task> findActiveTasksByDate(LocalDate date);
    
    @Query("SELECT t FROM Task t WHERE t.dueDate < ?1 AND t.status NOT IN ('COMPLETED', 'OVERDUE')")
    List<Task> findOverdueTasks(LocalDate date);
    
    @Query("SELECT t FROM Task t WHERE t.scheduledDate = ?1 ORDER BY " +
           "CASE WHEN t.status = 'IN_PROGRESS' THEN 1 " +
           "WHEN t.status = 'NOT_STARTED' THEN 2 " +
           "WHEN t.status = 'PENDING' THEN 3 " +
           "WHEN t.status = 'COMPLETED' THEN 4 " +
           "WHEN t.status = 'OVERDUE' THEN 5 END")
    List<Task> findTasksByDateOrderByStatusPriority(LocalDate date);

    @Query("SELECT t FROM Task t WHERE t.scheduledDate <= ?1 ORDER BY " +
           "CASE WHEN t.status = 'IN_PROGRESS' THEN 1 " +
           "WHEN t.status = 'NOT_STARTED' THEN 2 " +
           "WHEN t.status = 'PENDING' THEN 3 " +
           "WHEN t.status = 'COMPLETED' THEN 4 " +
           "WHEN t.status = 'OVERDUE' THEN 5 END")
    List<Task> findTasksByScheduledDateLessThanEqualOrderByStatusPriority(LocalDate date);

    List<Task> findByScheduledDateLessThanEqual(LocalDate date);
    
    List<Task> findByStatus(TaskStatus status);

    List<Task> findByStatusAndScheduledDateBetween(TaskStatus status, LocalDate startDate, LocalDate endDate);

    List<Task> findByScheduledDateBetween(LocalDate startDate, LocalDate endDate);

    List<Task> findByStatusAndDueDateBefore(TaskStatus status, LocalDate date);

    List<Task> findByDueDateAndStatusIn(LocalDate dueDate, Collection<TaskStatus> statuses);

    @Query("SELECT t FROM Task t WHERE t.scheduledDate < ?1 AND t.scheduledDate >= ?2 ORDER BY t.scheduledDate DESC, " +
           "CASE WHEN t.status = 'IN_PROGRESS' THEN 1 " +
           "WHEN t.status = 'NOT_STARTED' THEN 2 " +
           "WHEN t.status = 'PENDING' THEN 3 " +
           "WHEN t.status = 'COMPLETED' THEN 4 " +
           "WHEN t.status = 'OVERDUE' THEN 5 END")
    List<Task> findPastTasksInDateRange(LocalDate beforeDate, LocalDate afterDate);

    /**
     * 检查指定模板在指定日期是否已经生成过任务
     */
    boolean existsBySourceTemplateIdAndScheduledDate(Long sourceTemplateId, LocalDate scheduledDate);
}