#!/bin/bash

echo "=== 系统性能检查脚本 ==="
echo "检查时间: $(date)"
echo ""

echo "=== 1. CPU信息 ==="
echo "CPU核心数: $(nproc)"
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}'
echo ""

echo "=== 2. 内存信息 ==="
free -h
echo ""

echo "=== 3. 磁盘信息 ==="
df -h
echo ""

echo "=== 4. 磁盘I/O性能测试 ==="
echo "写入测试 (1GB):"
time dd if=/dev/zero of=/tmp/test_write bs=1M count=1024 2>&1 | grep -E "(copied|MB/s)"
echo "读取测试:"
time dd if=/tmp/test_write of=/dev/null bs=1M 2>&1 | grep -E "(copied|MB/s)"
rm -f /tmp/test_write
echo ""

echo "=== 5. 网络延迟测试 ==="
echo "本地回环延迟:"
ping -c 3 127.0.0.1 | grep "time="
echo ""

echo "=== 6. Java进程信息 ==="
if pgrep -f "spring-boot" > /dev/null; then
    echo "Spring Boot进程存在"
    ps aux | grep spring-boot | grep -v grep
    echo ""
    echo "JVM内存使用:"
    jps -v | grep -i spring
else
    echo "未找到Spring Boot进程"
fi
echo ""

echo "=== 7. 端口监听状态 ==="
netstat -tlnp | grep :18080
echo ""

echo "=== 8. 系统负载 ==="
uptime
echo ""

echo "=== 检查完成 ==="
