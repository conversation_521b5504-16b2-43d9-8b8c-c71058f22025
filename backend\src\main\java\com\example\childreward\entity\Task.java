package com.example.childreward.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Duration;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tasks")
public class Task {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的计划任务模板ID
     */
    private Long sourceTemplateId;
    
    @Column(nullable = false, length = 50)
    private String title;
    
    @Column(length = 255)
    private String description;
    
    @Column(name = "expected_minutes", nullable = false)
    private Integer expectedMinutes;
    
    @Column(name = "base_points", nullable = false)
    private Integer basePoints;
    
    @Column(name = "due_time", nullable = false)
    private LocalTime dueTime;
    
    @Column(name = "due_date", nullable = false)
    private LocalDate dueDate;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TaskStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TaskType taskType;
    
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @Column(name = "actual_points")
    private Integer actualPoints;
    
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "scheduled_date")
    private LocalDate scheduledDate;
    
    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
        if (this.status == null) {
            this.status = TaskStatus.NOT_STARTED;
        }
        if (this.scheduledDate == null) {
            this.scheduledDate = LocalDate.now();
        }
    }
    
    /**
     * 获取任务实际耗时（分钟）
     * 如果任务未开始或未完成，则返回0
     * 不满1分钟按1分钟计算
     */
    @Transient
    public Integer getActualMinutes() {
        if (startTime == null || endTime == null) {
            return 0;
        }

        // 计算总秒数
        long totalSeconds = Duration.between(startTime, endTime).getSeconds();

        // 如果总秒数为0或负数，返回0
        if (totalSeconds <= 0) {
            return 0;
        }

        // 转换为分钟，不满1分钟按1分钟计算（向上取整）
        return (int) Math.ceil(totalSeconds / 60.0);
    }



}