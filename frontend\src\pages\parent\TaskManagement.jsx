import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { taskApi } from '../../api/apiService';
import { format, addDays, subDays } from 'date-fns';
import RichTextRenderer from '../../components/common/RichTextRenderer';
import RichTextEditor from '../../components/RichTextEditor';

// 模拟任务数据
const mockTasks = [
  {
    id: 1,
    title: '完成数学作业',
    expectedMinutes: 30,
    basePoints: 10,
    dueTime: '18:00',
    description: '完成数学课本P15-P17的习题',
    active: true,
    taskType: 'required'
  },
  {
    id: 2,
    title: '整理书桌',
    expectedMinutes: 15,
    basePoints: 5,
    dueTime: '19:00',
    description: '整理书桌，摆放整齐文具和书本',
    active: true,
    taskType: 'required'
  },
  {
    id: 3,
    title: '阅读一章书',
    expectedMinutes: 20,
    basePoints: 8,
    dueTime: '20:00',
    description: '阅读指定书籍的一个章节',
    active: true,
    taskType: 'optional'
  }
];

const TaskManagement = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [expandedRow, setExpandedRow] = useState(null);
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [showAllTasks, setShowAllTasks] = useState(false);
  const [formValues, setFormValues] = useState({
    title: '',
    expectedMinutes: 30,
    basePoints: 1,
    dueTime: '18:00',
    scheduledDate: format(new Date(), 'yyyy-MM-dd'),
    description: '',
    active: true,
    taskType: 'REQUIRED'
  });

  // 根据状态返回对应的中文标签
  const getStatusLabel = (status) => {
    if (!status) return '未知';

    switch(status.toUpperCase()) {
      case 'NOT_STARTED': return '未开始';
      case 'IN_PROGRESS': return '进行中';
      case 'PENDING': return '待审批';
      case 'COMPLETED': return '已完成';
      case 'REJECTED': return '已作废'; // 审批拒绝，质量不过关
      case 'OVERDUE': return '已过期'; // 任务过期，没有完成
      default: return status;
    }
  };

  // 从API获取任务列表
  const fetchTasks = async (date) => {
    setLoading(true);
    try {
      // 如果showAllTasks为true，则获取所有任务，否则按日期获取
      const response = showAllTasks 
        ? await taskApi.getAllTasks()
        : await taskApi.getTasks(date || selectedDate);
      
      // 转换任务数据为前端格式
      const formattedTasks = response.data.map(task => {
        let formattedDueTime = '18:00'; // 默认值
        if (task.dueTime) {
          const timeDate = new Date(`2023-01-01T${task.dueTime}`);
          if (!isNaN(timeDate.getTime())) {
            formattedDueTime = format(timeDate, 'HH:mm');
          } else {
            console.warn(`后端返回了无效的dueTime: ${task.dueTime}`);
          }
        }

        let formattedScheduledDate = null;
        if (task.scheduledDate) {
          // 为避免时区问题，我们附加一个标准时间来解析日期
          const dateObj = new Date(`${task.scheduledDate}T00:00:00`);
          if (!isNaN(dateObj.getTime())) {
            formattedScheduledDate = format(dateObj, 'yyyy-MM-dd');
          } else {
            console.warn(`后端返回了无效的scheduledDate: ${task.scheduledDate}`);
          }
        }

        return {
          id: task.id,
          title: task.title,
          expectedMinutes: task.expectedMinutes,
          basePoints: task.basePoints,
          dueTime: formattedDueTime,
          description: task.description || '',
          active: task.status !== 'OVERDUE',
          status: task.status, // 保存原始状态
          taskType: task.taskType, // 不再转换为小写
          actualMinutes: task.actualMinutes || 0, // 获取实际耗时
          startTime: task.startTime, // 保存开始时间
          endTime: task.endTime, // 保存结束时间
          scheduledDate: formattedScheduledDate
        };
      });
      setTasks(formattedTasks);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      // 如果API请求失败，可以考虑使用模拟数据
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [selectedDate, showAllTasks]);

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
    setShowAllTasks(false); // 选择日期时关闭"显示所有任务"
  };

  const handleShowAllTasksChange = (e) => {
    setShowAllTasks(e.target.checked);
  };

  const handlePrevDay = () => {
    const prevDay = subDays(new Date(selectedDate), 1);
    setSelectedDate(format(prevDay, 'yyyy-MM-dd'));
    setShowAllTasks(false);
  };

  const handleNextDay = () => {
    const nextDay = addDays(new Date(selectedDate), 1);
    setSelectedDate(format(nextDay, 'yyyy-MM-dd'));
    setShowAllTasks(false);
  };

  const handleTodayClick = () => {
    setSelectedDate(format(new Date(), 'yyyy-MM-dd'));
    setShowAllTasks(false);
  };

  const handleAddTask = () => {
    setEditingTask(null);
    setFormValues({
      title: '',
      expectedMinutes: 30,
      basePoints: 1,
      dueTime: '23:59',
      scheduledDate: format(new Date(), 'yyyy-MM-dd'),
      description: '',
      active: true,
      taskType: 'REQUIRED'
    });
    setShowModal(true);
  };

  const handleEditTask = (task) => {
    setEditingTask(task);
    setFormValues({
      title: task.title,
      expectedMinutes: task.expectedMinutes,
      basePoints: task.basePoints,
      dueTime: task.dueTime,
      scheduledDate: task.scheduledDate || format(new Date(), 'yyyy-MM-dd'),
      description: task.description || '',
      active: task.active,
      taskType: task.taskType === 'REQUIRED' ? 'REQUIRED' : 'OPTIONAL'
    });
    setShowModal(true);
  };

  const handleDeleteTask = async (taskId) => {
    if (window.confirm('确定要删除这个任务吗？')) {
      try {
        await taskApi.deleteTask(taskId);
        setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
      } catch (error) {
        console.error('删除任务失败:', error);
        alert('删除任务失败，请稍后再试');
      }
    }
  };

  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormValues({
      ...formValues,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // 转换表单数据为API格式
    const taskData = {
      title: formValues.title,
      description: formValues.description,
      expectedMinutes: parseInt(formValues.expectedMinutes),
      basePoints: parseInt(formValues.basePoints),
      dueTime: formValues.dueTime,
      dueDate: formValues.scheduledDate,
      scheduledDate: formValues.scheduledDate,
      taskType: formValues.taskType.toUpperCase()
    };

    try {
      if (editingTask) {
        // 更新任务
        const response = await taskApi.updateTask(editingTask.id, taskData);
        // 使用后端返回的完整数据更新UI
        const updatedTask = response.data;
        setTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === editingTask.id ? { ...task, ...updatedTask } : task
          )
        );
      } else {
        // 添加任务
        const response = await taskApi.createTask(taskData);
        // 使用后端返回的完整数据构建新任务对象
        const newTask = {
          ...response.data,
          active: true // 假设新任务总是激活的
        };
        setTasks(prevTasks => [...prevTasks, newTask]);
      }
      setShowModal(false);
    } catch (error) {
      console.error('保存任务失败:', error);
      alert('保存任务失败，请稍后再试');
    }
  };

  if (loading) {
    return <LoadingMessage>加载中...</LoadingMessage>;
  }

  return (
    <TaskManagementContainer>
      <Header>
        <Title>任务管理</Title>
        <HeaderControls>
          <RefreshButton 
            onClick={() => {
              setLoading(true);
              fetchTasks().finally(() => setLoading(false));
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <RefreshIcon>🔄</RefreshIcon>
            刷新
          </RefreshButton>
          <AddButton 
            onClick={handleAddTask}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <PlusIcon>+</PlusIcon> 新增任务
          </AddButton>
        </HeaderControls>
      </Header>

      <DateFilterContainer>
        <DatePickerWrapper>
          <DatePickerLabel>选择日期:</DatePickerLabel>
          <DatePicker
            type="date"
            value={selectedDate}
            onChange={handleDateChange}
          />
          <TodayButton onClick={handleTodayClick}>今天</TodayButton>
        </DatePickerWrapper>

        <DateNavigationGroup>
          <DateNavigationButton onClick={handlePrevDay}>
            <NavigationIcon>◀</NavigationIcon>
            前一天
          </DateNavigationButton>

          <DateNavigationButton onClick={handleNextDay}>
            后一天
            <NavigationIcon>▶</NavigationIcon>
          </DateNavigationButton>
        </DateNavigationGroup>

        <ShowAllTasksContainer>
          <CheckboxInput
            id="showAllTasks"
            type="checkbox"
            checked={showAllTasks}
            onChange={handleShowAllTasksChange}
          />
          <CheckboxLabel htmlFor="showAllTasks">显示所有任务</CheckboxLabel>
        </ShowAllTasksContainer>
      </DateFilterContainer>

      <TaskStatCard>
        <StatItem>
          <StatLabel>当前任务总数</StatLabel>
          <StatValue>{tasks.length}</StatValue>
        </StatItem>
        <StatItem>
          <StatLabel>激活任务</StatLabel>
          <StatValue>{tasks.filter(task => task.active).length}</StatValue>
        </StatItem>
        <StatItem>
          <StatLabel>今日可获得积分</StatLabel>
          <StatValue>{tasks.reduce((sum, task) => sum + (task.active ? task.basePoints : 0), 0)}</StatValue>
        </StatItem>
      </TaskStatCard>

      <TaskListContainer>
        <TaskListHeader>
          <TaskHeaderCell width="15%">任务名称</TaskHeaderCell>
          <TaskHeaderCell width="10%">预计时长</TaskHeaderCell>
          <TaskHeaderCell width="10%">实际耗时</TaskHeaderCell>
          <TaskHeaderCell width="10%">基础积分</TaskHeaderCell>
          <TaskHeaderCell width="10%">计划日期</TaskHeaderCell>
          <TaskHeaderCell width="10%">完成期限</TaskHeaderCell>
          <TaskHeaderCell width="10%">类型</TaskHeaderCell>
          <TaskHeaderCell width="10%">状态</TaskHeaderCell>
          <TaskHeaderCell width="15%">操作</TaskHeaderCell>
        </TaskListHeader>

        <TaskList>
          <AnimatePresence>
            {tasks.length > 0 ? (
              tasks.map(task => (
                <React.Fragment key={task.id}>
                  <TaskRow
                    onClick={() => setExpandedRow(expandedRow === task.id ? null : task.id)}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    isActive={task.active}
                    isExpanded={expandedRow === task.id}
                  >
                    <TaskCell width="15%">{task.title}</TaskCell>
                    <TaskCell width="10%">{task.expectedMinutes} 分钟</TaskCell>
                    <TaskCell width="10%">
                      {task.actualMinutes > 0 ? `${task.actualMinutes} 分钟` : '-'}
                    </TaskCell>
                    <TaskCell width="10%">{task.basePoints} 分</TaskCell>
                    <TaskCell width="10%">{task.scheduledDate || format(new Date(), 'yyyy-MM-dd')}</TaskCell>
                    <TaskCell width="10%">{task.dueTime}</TaskCell>
                    <TaskCell width="10%">
                      <TaskTypeBadge isRequired={task.taskType === 'REQUIRED'}>
                        {task.taskType === 'REQUIRED' ? '必做' : '选做'}
                      </TaskTypeBadge>
                    </TaskCell>
                    <TaskCell width="10%">
                      <StatusBadge status={task.status}>
                        {getStatusLabel(task.status)}
                      </StatusBadge>
                    </TaskCell>
                    <TaskCell width="15%">
                      <ActionButtons>
                        <ActionButton 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditTask(task);
                          }}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          color="primary"
                        >
                          编辑
                        </ActionButton>
                        <ActionButton 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTask(task.id);
                          }}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          color="danger"
                        >
                          删除
                        </ActionButton>
                      </ActionButtons>
                    </TaskCell>
                  </TaskRow>
                  <AnimatePresence>
                    {expandedRow === task.id && (
                      <DescriptionRow
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <DescriptionCell colSpan={9}>
                          <DescriptionTitle>任务说明</DescriptionTitle>
                          <RichTextRenderer content={task.description || '无'} maxHeight="400px" />
                        </DescriptionCell>
                      </DescriptionRow>
                    )}
                  </AnimatePresence>
                </React.Fragment>
              ))
            ) : (
              <EmptyTaskMessage>
                {showAllTasks ? '暂无任务数据' : `${selectedDate} 没有安排任务`}
              </EmptyTaskMessage>
            )}
          </AnimatePresence>
        </TaskList>
      </TaskListContainer>

      {/* 任务表单模态框 */}
      <AnimatePresence>
        {showModal && (
          <ModalOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <ModalContainer
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
            >
              <ModalHeader>
                <ModalTitle>{editingTask ? '编辑任务' : '新增任务'}</ModalTitle>
                <CloseButton onClick={() => setShowModal(false)}>×</CloseButton>
              </ModalHeader>

              <ModalBody>
                <TaskForm onSubmit={handleSubmit}>
                  <FormGroup>
                    <Label htmlFor="title">任务名称</Label>
                    <Input
                      id="title"
                      name="title"
                      value={formValues.title}
                      onChange={handleFormChange}
                      required
                    />
                  </FormGroup>

                  <FormRow>
                    <FormGroup>
                      <Label htmlFor="expectedMinutes">预计时长(分钟)</Label>
                      <Input
                        id="expectedMinutes"
                        name="expectedMinutes"
                        type="number"
                        min="1"
                        max="180"
                        value={formValues.expectedMinutes}
                        onChange={handleFormChange}
                        required
                      />
                    </FormGroup>

                    <FormGroup>
                      <Label htmlFor="basePoints">基础积分</Label>
                      <Input
                        id="basePoints"
                        name="basePoints"
                        type="number"
                        min="0"
                        max="100"
                        value={formValues.basePoints}
                        onChange={handleFormChange}
                        required
                      />
                    </FormGroup>

                    <FormGroup>
                      <Label htmlFor="dueTime">完成期限</Label>
                      <Input
                        id="dueTime"
                        name="dueTime"
                        type="time"
                        value={formValues.dueTime}
                        onChange={handleFormChange}
                        required
                      />
                    </FormGroup>
                  </FormRow>

                  <FormGroup>
                    <Label htmlFor="scheduledDate">计划日期</Label>
                    <Input
                      id="scheduledDate"
                      name="scheduledDate"
                      type="date"
                      value={formValues.scheduledDate}
                      onChange={handleFormChange}
                      required
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label htmlFor="description">任务说明</Label>
                    <RichTextEditor
                      value={formValues.description}
                      onChange={(value) => setFormValues(prev => ({ ...prev, description: value }))}
                      placeholder="例如：完成数学作业第15页，需要认真计算。支持粘贴图片！"
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label>任务类型</Label>
                    <RadioGroup>
                      <RadioContainer>
                        <RadioInput
                          type="radio"
                          id="taskTypeRequired"
                          name="taskType"
                          value="REQUIRED"
                          checked={formValues.taskType === 'REQUIRED'}
                          onChange={handleFormChange}
                        />
                        <RadioLabel htmlFor="taskTypeRequired">必做任务</RadioLabel>
                      </RadioContainer>
                      <RadioContainer>
                        <RadioInput
                          type="radio"
                          id="taskTypeOptional"
                          name="taskType"
                          value="OPTIONAL"
                          checked={formValues.taskType === 'OPTIONAL'}
                          onChange={handleFormChange}
                        />
                        <RadioLabel htmlFor="taskTypeOptional">选做任务</RadioLabel>
                      </RadioContainer>
                    </RadioGroup>
                    <InputDescription>必做任务必须完成，选做任务可以自愿完成</InputDescription>
                  </FormGroup>

                  <FormGroup>
                    <CheckboxContainer>
                      <CheckboxInput
                        id="active"
                        name="active"
                        type="checkbox"
                        checked={formValues.active}
                        onChange={handleFormChange}
                      />
                      <CheckboxLabel htmlFor="active">激活任务</CheckboxLabel>
                    </CheckboxContainer>
                  </FormGroup>

                  <ButtonGroup>
                    <CancelButton
                      type="button"
                      onClick={() => setShowModal(false)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      取消
                    </CancelButton>
                    <SubmitButton
                      type="submit"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {editingTask ? '更新' : '创建'}
                    </SubmitButton>
                  </ButtonGroup>
                </TaskForm>
              </ModalBody>
            </ModalContainer>
          </ModalOverlay>
        )}
      </AnimatePresence>
    </TaskManagementContainer>
  );
};

// 样式组件
const TaskManagementContainer = styled.div`
  padding: 1.5rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    margin-bottom: 1.5rem;
  }
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: ${parentTheme.textColor};
  margin: 0;

  @media (max-width: 768px) {
    font-size: 1.5rem;
    text-align: center;
  }
`;

const HeaderControls = styled.div`
  display: flex;
  gap: 1rem;

  @media (max-width: 768px) {
    justify-content: center;
    flex-wrap: wrap;
  }
`;

const RefreshButton = styled(motion.button)`
  display: flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-weight: 600;
  cursor: pointer;

  @media (max-width: 768px) {
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
`;

const RefreshIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
  line-height: 1;
`;

const AddButton = styled(motion.button)`
  display: flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-weight: 600;
  cursor: pointer;

  @media (max-width: 768px) {
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
`;

const PlusIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
  line-height: 1;
`;

const LoadingMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: ${parentTheme.textColor};
`;

const TaskStatCard = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 1.5rem;
  }
`;

const StatItem = styled.div`
  text-align: center;
  padding: 1rem;

  @media (max-width: 768px) {
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: ${parentTheme.primaryColor};
`;

const TaskListContainer = styled.div`
  background-color: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  @media (max-width: 768px) {
    background-color: transparent;
    box-shadow: none;
    border-radius: 0;
  }
`;

const TaskListHeader = styled.div`
  display: flex;
  padding: 1rem;
  background-color: #f5f7fa;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #666;

  @media (max-width: 768px) {
    display: none; /* 手机端隐藏表头 */
  }
`;

const TaskHeaderCell = styled.div`
  width: ${props => props.width};
  padding: 0 0.5rem;
`;

const TaskList = styled.div`
  max-height: 500px;
  overflow-y: auto;

  @media (max-width: 768px) {
    max-height: none;
    padding: 0.5rem;
    background-color: transparent;
  }
`;

const TaskRow = styled(motion.div)`
  display: flex;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  opacity: ${props => props.isActive ? 1 : 0.6};
  cursor: pointer;
  background-color: ${props => props.isExpanded ? '#f5f7fa' : 'white'};

  &:hover {
    background-color: ${props => props.isExpanded ? '#f0f2f5' : 'rgba(0, 0, 0, 0.02)'};
  }

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: ${parentTheme.borderRadius};
    border: 1px solid #eee;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const TaskCell = styled.div`
  width: ${props => props.width};
  padding: 0 0.5rem;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    width: 100% !important;
    padding: 0.25rem 0;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
      margin-top: 0.5rem;
      justify-content: center;
    }

    /* 为移动端添加标签 */
    &:before {
      content: attr(data-label);
      font-weight: 600;
      color: #666;
      font-size: 0.85rem;
      min-width: 80px;
    }

    &:first-child:before {
      content: "任务名称: ";
    }

    &:nth-child(2):before {
      content: "预计时长: ";
    }

    &:nth-child(3):before {
      content: "实际耗时: ";
    }

    &:nth-child(4):before {
      content: "基础积分: ";
    }

    &:nth-child(5):before {
      content: "计划日期: ";
    }

    &:nth-child(6):before {
      content: "完成期限: ";
    }

    &:nth-child(7):before {
      content: "类型: ";
    }

    &:nth-child(8):before {
      content: "状态: ";
    }

    &:nth-child(9):before {
      content: "";
    }
  }
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  color: white;
  background-color: ${props => {
    // 根据状态设置不同的背景色
    switch(props.status?.toUpperCase()) {
      case 'NOT_STARTED': return '#8e44ad'; // 紫色
      case 'IN_PROGRESS': return '#3498db'; // 蓝色
      case 'PENDING': return '#f39c12'; // 橙色
      case 'COMPLETED': return '#2ecc71'; // 绿色
      case 'REJECTED': return '#e74c3c'; // 红色
      case 'OVERDUE': return '#95a5a6'; // 灰色
      default: return props.isActive ? '#2ecc71' : '#95a5a6';
    }
  }};
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  min-width: 70px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled(motion.button)`
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  background-color: ${props => props.color === 'danger' ? 'rgba(255, 59, 48, 0.1)' : 'rgba(0, 122, 255, 0.1)'};
  color: ${props => props.color === 'danger' ? '#ff3b30' : '#007aff'};
  
  &:hover {
    background-color: ${props => props.color === 'danger' ? 'rgba(255, 59, 48, 0.2)' : 'rgba(0, 122, 255, 0.2)'};
  }
`;

// 模态框样式
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled(motion.div)`
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  background-color: white;
  border-radius: ${parentTheme.borderRadius};
  overflow: hidden;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.25rem;
  color: ${parentTheme.textColor};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  
  &:hover {
    color: #666;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
`;

const TaskForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
`;

const Label = styled.label`
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
`;

const Input = styled.input`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const TextArea = styled.textarea`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
`;

const CheckboxInput = styled.input`
  margin-right: 0.5rem;
  width: 1.2rem;
  height: 1.2rem;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
  color: ${parentTheme.textColor};
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
`;

const BaseButton = styled(motion.button)`
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
`;

const CancelButton = styled(BaseButton)`
  background-color: #f5f5f5;
  color: #666;
  
  &:hover {
    background-color: #eee;
  }
`;

const SubmitButton = styled(BaseButton)`
  background: ${parentTheme.gradients.primary};
  color: white;
`;

// 添加任务类型单选按钮样式
const RadioGroup = styled.div`
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
`;

const RadioContainer = styled.div`
  display: flex;
  align-items: center;
`;

const RadioInput = styled.input`
  margin-right: 0.5rem;
  width: 1rem;
  height: 1rem;
`;

const RadioLabel = styled.label`
  font-size: 0.9rem;
  color: ${parentTheme.textColor};
`;

const InputDescription = styled.div`
  font-size: 0.8rem;
  color: #999;
  margin-top: 0.25rem;
`;

// 添加任务类型样式
const TaskTypeBadge = styled.span`
  padding: 0.3rem 0.6rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${props => props.isRequired ? 'rgba(255, 59, 48, 0.1)' : 'rgba(255, 204, 0, 0.1)'};
  color: ${props => props.isRequired ? '#ff3b30' : '#ffcc00'};
`;

const DescriptionRow = styled(motion.div)`
  background-color: #f9fafb;
  border-bottom: 1px solid #eee;
`;

const DescriptionCell = styled.div`
  padding: 1rem 1.5rem;
  color: #555;
`;

const DescriptionTitle = styled.h4`
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #333;
`;

const DescriptionText = styled.p`
  margin: 0;
  white-space: pre-wrap;
`;

// 新增的样式组件
const DateFilterContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: white;
  padding: 1rem;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem;
  }
`;

const DatePickerWrapper = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;

  @media (max-width: 768px) {
    order: 1;
    margin-bottom: 1rem;
  }
`;

const DateNavigationGroup = styled.div`
  display: flex;
  gap: 1rem;

  @media (max-width: 768px) {
    order: 2;
    justify-content: center;
    width: 100%;
  }
`;

const DatePickerLabel = styled.label`
  margin-right: 0.5rem;
  font-size: 0.9rem;
  color: #666;
`;

const DatePicker = styled.input`
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const DateNavigationButton = styled.button`
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }

  @media (max-width: 768px) {
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }
`;

const NavigationIcon = styled.span`
  margin: 0 0.3rem;
`;

const TodayButton = styled.button`
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
  background: ${parentTheme.gradients.secondary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }

  @media (max-width: 768px) {
    margin-left: 0.75rem;
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
`;

const ShowAllTasksContainer = styled.div`
  display: flex;
  align-items: center;
  margin-left: 1rem;

  @media (max-width: 768px) {
    order: 3;
    margin-left: 0;
    justify-content: center;
    width: 100%;
  }
`;

const EmptyTaskMessage = styled.div`
  padding: 2rem;
  text-align: center;
  color: #999;
  font-size: 1.1rem;
`;

export default TaskManagement; 